#!/usr/bin/env python3
"""测试类型切换功能。"""

from beezer.gui.views.inbound_view import DynamicInboundEditor
from beezer.type_model import InboundTypes


def test_type_switching():
    """测试类型切换时字段的更新。"""
    print("测试类型切换功能...")
    
    # 创建编辑器
    editor = DynamicInboundEditor(
        title="测试类型切换",
        initial_data={},
        on_save=lambda data: print(f"保存数据: {data}"),
        on_cancel=lambda: print("取消编辑")
    )
    
    # 测试不同类型的字段
    types_to_test = [
        InboundTypes.Modbus,
        InboundTypes.HttpClient,
        InboundTypes.Fanuc,
        InboundTypes.Siemens,
        InboundTypes.Focas
    ]
    
    for inbound_type in types_to_test:
        print(f"\n=== 测试 {inbound_type} 类型 ===")
        
        # 设置当前类型
        editor.current_type = inbound_type
        
        # 获取字段
        fields = editor._get_fields_for_type(inbound_type)
        
        print(f"字段数量: {len(fields)}")
        for field in fields:
            default_value = editor._get_default_value_for_field(field)
            print(f"  - {field.name}: {field.label} ({field.field_type})")
            if field.name in ["requests", "points", "param_config"]:
                print(f"    默认值长度: {len(str(default_value))} 字符")
            else:
                print(f"    默认值: {default_value}")


def test_field_validation():
    """测试字段验证。"""
    print("\n" + "="*50)
    print("测试字段验证...")
    
    editor = DynamicInboundEditor(
        title="测试验证",
        initial_data={"type": InboundTypes.HttpClient}
    )
    
    # 模拟字段控件
    class MockControl:
        def __init__(self, value):
            self.value = value
    
    # 设置当前类型
    editor.current_type = InboundTypes.HttpClient
    
    # 模拟字段控件
    editor.field_controls = {
        "id": MockControl("test_id"),
        "interval": MockControl("60"),
        "requests": MockControl('''[
  {
    "method": "GET",
    "name": "test",
    "url": "http://example.com/api",
    "headers": {},
    "params": {},
    "data": null,
    "content_type": null
  }
]''')
    }
    
    # 验证字段
    is_valid, error_msg = editor._validate_fields()
    print(f"验证结果: {is_valid}")
    if not is_valid:
        print(f"错误信息: {error_msg}")
    
    # 收集数据
    data = {"type": editor.current_type}
    fields = editor._get_fields_for_type(editor.current_type)
    
    for field in fields:
        if field.name in editor.field_controls:
            value = editor._get_field_value(field)
            if value is not None and value != "":
                data[field.name] = value
    
    print(f"收集的数据: {data}")
    print(f"requests 类型: {type(data.get('requests'))}")


if __name__ == "__main__":
    test_type_switching()
    test_field_validation()
    print("\n测试完成!")
