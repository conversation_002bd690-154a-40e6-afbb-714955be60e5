apps: []
inbounds:
  1:
    type: !!python/object/apply:beezer.type_model.InboundTypes
    - siemens
    id: ************
    model: 828D
    ip: !!python/object/apply:ipaddress.IPv4Address
    - 3232238089
    port: 102
    interval: 1
    param_config:
      path:
      - axes:
        - name: X
          num: 1
        - name: Y
          num: 2
        - name: Z
          num: 3
        - name: A
          num: 5
        - name: C
          num: 6
        spindles:
        - name: S1
          num: 4
  ************:
    type: !!python/object/apply:beezer.type_model.InboundTypes
    - http_client
    id: ************
    interval: 1
    requests:
    - method: GET
      name: example
      url: http://example.com/api
      headers: {}
      params: {}
      data: null
      content_type: null
outbounds:
  custom_ioteq:
    type: custom_ioteq
    config:
      api_host: http://*************:9890/eq/eqEquipmentAccTabs/
      odbc_url: mssql+pyodbc://sa:123456@*************:1433/test?driver=ODBC+Driver+17+for+SQL+Server
rules:
- name: syntec_status
  rules:
  - source_name: current
    source:
      source: 2 if var1 else 0
      placeholder:
        var1: $.data
      mapping: {}
      default: null
    target_name: null
    target: $.State
    type: expr
  - source_name: current
    source:
      source: 2 if var1 != 1024 else 0
      placeholder:
        var1: $.data.result
      mapping: {}
      default: null
    target_name: null
    target: $.State
    type: expr
  - source_name: current
    source:
      source: $.data.path[0].status
      placeholder: {}
      mapping:
        ACTIVE: 1
      default: 2
    target_name: null
    target: $.State
    type: map
  - source_name: null
    source: $.id
    target_name: null
    target: $.id
    type: reference
  - source_name: null
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: null
    target_name: null
    target: $.CreateDate
    type: expr
  - source_name: null
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: null
    target_name: null
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
- name: syntec_count
  rules:
  - source_name: current
    source: $.data.part_count
    target_name: null
    target: $.Count
    type: reference
  - source_name: null
    source: $.id
    target_name: null
    target: $.id
    type: reference
  - source_name: null
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: null
    target_name: null
    target: $.CreateDate
    type: expr
  - source_name: null
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: null
    target_name: null
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
- name: syntec_alarm
  rules:
  - source_name: current
    source:
      source: 1 if (var1 and len(var1)) or var2 == 'TRIGGERED' else 0
      placeholder:
        var1: $.data.alarm
        var2: $.data.emg
      mapping: {}
      default: null
    target_name: null
    target: $.State
    type: expr
  - source_name: null
    source: $.id
    target_name: null
    target: $.id
    type: reference
  - source_name: null
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: null
    target_name: null
    target: $.CreateDate
    type: expr
  - source_name: null
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: null
    target_name: null
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
- name: syntec_current
  rules:
  - source_name: current
    source: $.data.path[0].main_prog
    target_name: null
    target: $.main_prog
    type: reference
  - source_name: current
    source: $.data.path[0].cur_prog
    target_name: null
    target: $.cur_prog
    type: reference
  - source_name: current
    source: $.data.path[0].cur_seq
    target_name: null
    target: $.cur_seq
    type: reference
  - source_name: current
    source: $.data.path[0].ov_feed
    target_name: null
    target: $.ov_feed
    type: reference
  - source_name: current
    source: $.data.path[0].act_feed
    target_name: null
    target: $.act_feed
    type: reference
  - source_name: current
    source: $.data.path[0].spindles
    target_name: null
    target: $.spindles
    type: reference
  - source_name: current
    source: $.data.path[0].axes
    target_name: null
    target: $.axes
    type: reference
  - source_name: current
    source: $.data.emg
    target_name: null
    target: $.emg
    type: reference
  - source_name: current
    source: $.data.mode
    target_name: null
    target: $.mode
    type: reference
  - source_name: current
    source: $.data.alarm
    target_name: null
    target: $.alarm
    type: reference
  - source_name: null
    source: $.id
    target_name: null
    target: $.id
    type: reference
  - source_name: null
    source:
      source: TIMESTAMP_TO_STR(var1)
      placeholder:
        var1: $.timestamp
      mapping: {}
      default: null
    target_name: null
    target: $.CreateDate
    type: expr
  - source_name: null
    source:
      source: UUID1()
      placeholder: {}
      mapping: {}
      default: null
    target_name: null
    target: $.Uuid
    type: expr
  trigger:
    mode: change
    type: any
    exclude:
    - $.CreateDate
    - $.Uuid
flows:
- name: flow01
  inbounds:
  - 1
  outbound: custom_ioteq
  rules:
  - name: syntec_status
    trigger: null
    action: IOTEqStatues
    merge_strategy: null
  - name: syntec_alarm
    trigger: null
    action: IOTEqWarning
    merge_strategy: null
  - name: syntec_count
    trigger: null
    action: IOTEqProduceCount
    merge_strategy: null
  - name: syntec_current
    trigger: null
    action: IOTEqMachiningParams
    merge_strategy: null
server_port: 9999
version: 1
