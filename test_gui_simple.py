#!/usr/bin/env python3
"""简单的GUI测试脚本，测试动态编辑器功能。"""

import flet as ft
from beezer.gui.views.inbound_view import DynamicInboundEditor
from beezer.type_model import InboundTypes


def main(page: ft.Page):
    page.title = "动态编辑器测试"
    page.window_width = 800
    page.window_height = 600
    
    def on_save(data):
        print(f"保存数据: {data}")
        page.show_snack_bar(ft.SnackBar(content=ft.Text(f"保存成功: {data}")))
    
    def on_cancel():
        print("取消编辑")
        page.show_snack_bar(ft.SnackBar(content=ft.Text("取消编辑")))
    
    # 创建动态编辑器
    editor = DynamicInboundEditor(
        title="测试动态编辑器",
        initial_data={"type": InboundTypes.Modbus},
        on_save=on_save,
        on_cancel=on_cancel
    )
    
    # 设置页面引用
    editor.page = page
    
    # 构建编辑器界面
    editor_content = editor.build()
    
    # 添加到页面
    page.add(editor_content)


if __name__ == "__main__":
    ft.app(target=main)
