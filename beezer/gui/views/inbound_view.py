"""Inbound 配置视图。

提供 Inbound 配置的管理界面，包括添加、编辑、删除等功能。
"""

import flet as ft
import time  # Added for test_text_widget uniqueness
from typing import Dict, Any, Optional, Callable, List, Union, Literal
from loguru import logger

from beezer.gui.components.config_editor import Config<PERSON>ield
from beezer.gui.config_manager import ConfigManager
from beezer.type_model import (
    InboundTypes,
    ModbusConfig,
    HttpClientConfig,
    FanucConfig,
    SiemensConfig,
    FocasConfig,
    InboundConfig,
)


class DynamicInboundEditor:
    """动态 Inbound 编辑器，根据类型显示不同的配置字段。"""

    def __init__(
        self,
        title: str,
        initial_data: Optional[Dict[str, Any]] = None,
        on_save: Optional[Callable[[Dict[str, Any]], None]] = None,
        on_cancel: Optional[Callable[[], None]] = None,
    ):
        """初始化动态编辑器。

        Args:
            title: 编辑器标题
            initial_data: 初始数据
            on_save: 保存回调函数
            on_cancel: 取消回调函数
        """

        self.title = title
        self.initial_data = initial_data or {}
        self.on_save = on_save
        self.on_cancel = on_cancel
        self.current_type = self.initial_data.get("type", "")
        self.error_text = ft.Text("", color=ft.Colors.RED, visible=False)

        # 控件引用
        self.type_dropdown = None
        self.fields_container = None
        self.field_controls: Dict[str, ft.Control] = {}

        # 类型到配置类的映射
        self.config_classes = {
            InboundTypes.Modbus: ModbusConfig,
            InboundTypes.HttpClient: HttpClientConfig,
            InboundTypes.Fanuc: FanucConfig,
            InboundTypes.Siemens: SiemensConfig,
            InboundTypes.Focas: FocasConfig,
        }

    def build(self):
        """构建编辑器界面。"""
        # 类型选择下拉框
        self.type_dropdown = ft.Dropdown(
            label="Inbound 类型 *",
            value=self.current_type,
            options=[ft.dropdown.Option(t.value, t.value) for t in InboundTypes],
            on_change=self._on_type_change,
            width=200,
        )

        # 动态字段容器
        self.fields_container = ft.Column(spacing=15)

        # 按钮
        buttons = ft.Row(
            [
                ft.ElevatedButton(
                    text="保存",
                    icon=ft.Icons.SAVE,
                    on_click=self._on_save_click,
                ),
                ft.OutlinedButton(
                    text="取消",
                    icon=ft.Icons.CANCEL,
                    on_click=self._on_cancel_click,
                ),
            ],
            alignment=ft.MainAxisAlignment.END,
        )

        # 初始化字段
        self._update_fields()

        return ft.Container(
            content=ft.Column(
                [
                    ft.Text(self.title, size=20, weight=ft.FontWeight.BOLD),
                    ft.Divider(),
                    self.type_dropdown,
                    ft.Divider(),
                    self.fields_container,
                    self.error_text,
                    ft.Divider(),
                    buttons,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
            ),
            padding=20,
        )

    def _on_type_change(self, e):
        """类型选择变化事件。"""
        self.current_type = e.control.value
        self._update_fields()
        self.update()

    def update(self):
        """更新界面。"""
        if hasattr(self, "page") and self.page:
            self.page.update()

    def _update_fields(self):
        """根据当前类型更新字段。"""
        self.fields_container.controls.clear()
        self.field_controls.clear()

        if not self.current_type:
            return

        # 获取类型特定的字段
        fields = self._get_fields_for_type(self.current_type)

        for field in fields:
            control = self._create_field_control(field)
            self.field_controls[field.name] = control

            # 创建字段容器
            field_container = ft.Column(
                [
                    ft.Text(
                        field.label + ("*" if field.required else ""),
                        weight=ft.FontWeight.BOLD,
                        size=14,
                    ),
                    control,
                ],
                spacing=5,
            )

            # 添加帮助文本
            if field.help_text:
                field_container.controls.append(
                    ft.Text(
                        field.help_text,
                        size=12,
                        color=ft.Colors.GREY_600,
                        italic=True,
                    )
                )

            self.fields_container.controls.append(field_container)

    def _get_fields_for_type(self, inbound_type: str) -> List[ConfigField]:
        """根据类型获取配置字段。

        Args:
            inbound_type: Inbound 类型

        Returns:
            List[ConfigField]: 配置字段列表
        """
        if inbound_type not in self.config_classes:
            return []

        config_class = self.config_classes[inbound_type]
        fields = []

        # 从Pydantic模型获取字段定义
        for field_name, field_info in config_class.model_fields.items():
            # 跳过type字段，因为它是固定的
            if field_name == "type":
                continue

            config_field = self._create_config_field_from_model(
                field_name, field_info, config_class
            )
            if config_field:
                fields.append(config_field)

        return fields

    def _create_config_field_from_model(
        self, field_name: str, field_info: Any, config_class: type
    ) -> Optional[ConfigField]:
        """从Pydantic模型字段创建ConfigField。

        Args:
            field_name: 字段名
            field_info: Pydantic字段信息
            config_class: 配置类

        Returns:
            Optional[ConfigField]: 配置字段
        """
        try:
            # 获取字段注解
            annotation = field_info.annotation

            # 获取默认值
            default_value = (
                field_info.default if field_info.default is not ... else None
            )

            # 判断是否必填
            required = field_info.is_required()

            # 获取字段描述
            description = field_info.description or f"{field_name} 配置"

            # 根据类型确定字段类型和选项
            field_type, options = self._determine_field_type_and_options(annotation)

            return ConfigField(
                name=field_name,
                label=self._get_field_label(field_name),
                field_type=field_type,
                required=required,
                default=default_value,
                options=options,
                help_text=description,
            )

        except Exception as e:
            logger.warning(f"无法为字段 {field_name} 创建配置字段: {e}")
            return None

    def _determine_field_type_and_options(
        self, annotation: Any
    ) -> tuple[str, List[str]]:
        """根据类型注解确定字段类型和选项。

        Args:
            annotation: 类型注解

        Returns:
            tuple[str, List[str]]: (字段类型, 选项列表)
        """
        # 处理Union类型（如 str | None）
        if hasattr(annotation, "__origin__") and annotation.__origin__ is Union:
            # 获取非None的类型
            args = [arg for arg in annotation.__args__ if arg is not type(None)]
            if args:
                annotation = args[0]

        # 处理Literal类型
        if hasattr(annotation, "__origin__") and annotation.__origin__ is Literal:
            return "dropdown", list(annotation.__args__)

        # 处理基本类型
        if annotation is str or str(annotation) == "str":
            return "text", []
        elif annotation is int or str(annotation) == "int":
            return "number", []
        elif annotation is float or str(annotation) == "float":
            return "number", []
        elif annotation is bool or str(annotation) == "bool":
            return "checkbox", []

        # 处理IP地址类型
        if hasattr(annotation, "__name__") and "Address" in str(annotation):
            return "text", []

        # 处理List类型
        if hasattr(annotation, "__origin__") and annotation.__origin__ is list:
            return "textarea", []

        # 处理Dict类型
        if hasattr(annotation, "__origin__") and annotation.__origin__ is dict:
            return "textarea", []

        # 默认为文本字段
        return "text", []

    def _get_field_label(self, field_name: str) -> str:
        """获取字段的显示标签。

        Args:
            field_name: 字段名

        Returns:
            str: 显示标签
        """
        label_map = {
            "id": "ID",
            "ip": "IP地址",
            "port": "端口",
            "proto": "协议",
            "connect": "连接方式",
            "slave": "从站地址",
            "interval": "采集间隔(秒)",
            "timeout": "超时时间(秒)",
            "model": "设备型号",
            "requests": "HTTP请求配置",
            "points": "Modbus点位配置",
            "param_config": "参数配置",
        }
        return label_map.get(field_name, field_name.replace("_", " ").title())

    def _create_field_control(self, field: ConfigField) -> ft.Control:
        """创建字段控件。"""
        initial_value = self.initial_data.get(field.name, field.default)

        if field.field_type == "text":
            return ft.TextField(
                value=str(initial_value) if initial_value is not None else "",
                hint_text=f"请输入{field.label}",
                border_radius=5,
            )
        elif field.field_type == "number":
            return ft.TextField(
                value=str(initial_value) if initial_value is not None else "",
                hint_text=f"请输入{field.label}",
                keyboard_type=ft.KeyboardType.NUMBER,
                border_radius=5,
            )
        elif field.field_type == "dropdown":
            return ft.Dropdown(
                value=str(initial_value) if initial_value is not None else None,
                options=[ft.dropdown.Option(opt) for opt in field.options],
                hint_text=f"请选择{field.label}",
                border_radius=5,
            )
        elif field.field_type == "checkbox":
            return ft.Checkbox(
                value=bool(initial_value) if initial_value is not None else False,
                label=field.label,
            )
        elif field.field_type == "textarea":
            return ft.TextField(
                value=str(initial_value) if initial_value is not None else "",
                hint_text=f"请输入{field.label}",
                multiline=True,
                min_lines=3,
                max_lines=6,
                border_radius=5,
            )
        else:
            return ft.TextField(
                value=str(initial_value) if initial_value is not None else "",
                hint_text=f"请输入{field.label}",
                border_radius=5,
            )

    def _get_field_value(self, field: ConfigField) -> Any:
        """获取字段值。"""
        control = self.field_controls[field.name]

        if field.field_type == "checkbox":
            return control.value
        elif field.field_type == "number":
            try:
                value = control.value
                return (
                    int(value)
                    if value and "." not in value
                    else float(value)
                    if value
                    else None
                )
            except ValueError:
                return None
        else:
            return control.value

    def _validate_fields(self) -> tuple[bool, str]:
        """验证所有字段。"""
        # 验证类型选择
        if not self.current_type:
            return False, "必须选择 Inbound 类型"

        # 验证其他字段
        fields = self._get_fields_for_type(self.current_type)
        for field in fields:
            if field.name not in self.field_controls:
                continue

            value = self._get_field_value(field)

            # 检查必填字段
            if field.required and (value is None or value == ""):
                return False, f"{field.label} 是必填字段"

        return True, ""

    def _on_save_click(self, e):
        """保存按钮点击事件。"""
        # 验证字段
        is_valid, error_msg = self._validate_fields()

        if not is_valid:
            self.error_text.value = error_msg
            self.error_text.visible = True
            self.update()
            return

        # 收集数据
        data = {"type": self.current_type}
        fields = self._get_fields_for_type(self.current_type)

        for field in fields:
            if field.name in self.field_controls:
                value = self._get_field_value(field)
                if value is not None and value != "":
                    data[field.name] = value

        # 隐藏错误信息
        self.error_text.visible = False
        self.update()

        # 调用保存回调
        if self.on_save:
            try:
                self.on_save(data)
            except Exception as e:
                logger.error(f"保存配置失败: {e}")
                self.error_text.value = f"保存失败: {str(e)}"
                self.error_text.visible = True
                self.update()

    def _on_cancel_click(self, e):
        """取消按钮点击事件。"""
        if self.on_cancel:
            self.on_cancel()


class InboundView:
    """Inbound 配置视图。"""

    def __init__(self, config_manager: ConfigManager, page: ft.Page):
        """初始化 Inbound 视图。

        Args:
            config_manager: 配置管理器
            page: Flet页面对象
        """

        self.config_manager = config_manager
        self.page = page
        self.inbound_list = ft.ListView(expand=True, spacing=10, padding=20)
        self.selected_inbound_id: Optional[str] = None
        self.editor_dialog: Optional[ft.AlertDialog] = None

        # 类型到配置类的映射
        self.config_classes = {
            InboundTypes.Modbus: ModbusConfig,
            InboundTypes.HttpClient: HttpClientConfig,
            InboundTypes.Fanuc: FanucConfig,
            InboundTypes.Siemens: SiemensConfig,
            InboundTypes.Focas: FocasConfig,
        }

    def build(self):
        """构建视图界面。"""
        # 创建工具栏
        toolbar = ft.Row(
            [
                ft.Text("Inbound 配置", size=24, weight=ft.FontWeight.BOLD),
                ft.Container(expand=True),  # 占位符
                ft.ElevatedButton(
                    text="添加 Inbound",
                    icon=ft.Icons.ADD,
                    on_click=self._on_add_inbound,
                ),
                ft.OutlinedButton(
                    text="刷新",
                    icon=ft.Icons.REFRESH,
                    on_click=self._on_refresh,
                ),
            ]
        )

        # 创建主容器
        return ft.Column(
            [
                toolbar,
                ft.Divider(),
                ft.Container(
                    content=self.inbound_list,
                    expand=True,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=5,
                ),
            ]
        )

    def did_mount(self):
        """组件挂载后的回调。"""
        self._refresh_inbound_list()

    def _refresh_inbound_list(self):
        """刷新 Inbound 列表。"""
        self.inbound_list.controls.clear()

        inbounds = self.config_manager.get_inbounds()

        if not inbounds:
            self.inbound_list.controls.append(
                ft.Container(
                    content=ft.Text(
                        "暂无 Inbound 配置\n点击上方 '添加 Inbound' 按钮开始配置",
                        text_align=ft.TextAlign.CENTER,
                        size=16,
                        color=ft.Colors.GREY_600,
                    ),
                    alignment=ft.alignment.center,
                    height=200,
                )
            )
        else:
            for inbound_id, inbound_config in inbounds.items():
                self.inbound_list.controls.append(
                    self._create_inbound_card(inbound_id, inbound_config)
                )

        self.page.update()

    def _create_inbound_card(self, inbound_id: str, inbound_config: Any) -> ft.Card:
        """创建 Inbound 卡片。

        Args:
            inbound_id: Inbound ID
            inbound_config: Inbound 配置

        Returns:
            ft.Card: Inbound 卡片
        """
        # 获取配置信息
        config_type = inbound_config.type
        config_id = getattr(inbound_config, "id", inbound_id)

        # 创建配置详情
        details = []
        if hasattr(inbound_config, "ip"):
            details.append(f"IP: {inbound_config.ip}")
        if hasattr(inbound_config, "port"):
            details.append(f"Port: {inbound_config.port}")
        if hasattr(inbound_config, "interval"):
            details.append(f"Interval: {inbound_config.interval}s")

        detail_text = " | ".join(details) if details else "无详细信息"

        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    [
                        ft.Column(
                            [
                                ft.Text(
                                    f"{config_id} ({config_type})",
                                    size=16,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Text(
                                    detail_text,
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                ),
                            ],
                            expand=True,
                        ),
                        ft.Row(
                            [
                                ft.IconButton(
                                    icon=ft.Icons.EDIT,
                                    tooltip="编辑",
                                    on_click=lambda e,
                                    id=inbound_id: self._on_edit_inbound(id),
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.DELETE,
                                    tooltip="删除",
                                    icon_color=ft.Colors.RED,
                                    on_click=lambda e,
                                    id=inbound_id: self._on_delete_inbound(id),
                                ),
                            ]
                        ),
                    ]
                ),
                padding=15,
            ),
        )

    def _on_add_inbound(self, e):
        """添加 Inbound 按钮点击事件。"""
        self._show_inbound_editor()

    def _on_edit_inbound(self, inbound_id: str):
        """编辑 Inbound 按钮点击事件。"""
        logger.info(f"开始编辑 Inbound: {inbound_id}")
        try:
            inbounds = self.config_manager.get_inbounds()
            logger.info(
                f"获取到 Inbound 配置: {list(inbounds.keys()) if inbounds else '无'}"
            )
            if inbound_id in inbounds:
                logger.info(f"找到 Inbound ID: {inbound_id}，准备打开编辑器")
                self._show_inbound_editor(inbound_id, inbounds[inbound_id])
            else:
                logger.error(f"Inbound ID: {inbound_id} 未在配置中找到")
        except Exception as e:
            logger.exception(f"编辑 Inbound 时发生错误: {e}")

    def _on_delete_inbound(self, inbound_id: str):
        """删除 Inbound 按钮点击事件。"""

        def confirm_delete(e):
            if self.config_manager.remove_inbound(inbound_id):
                self._refresh_inbound_list()
                logger.info(f"删除 Inbound: {inbound_id}")
            dialog.open = False
            self.page.update()

        def cancel_delete(e):
            dialog.open = False
            self.page.update()

        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("确认删除"),
            content=ft.Text(f"确定要删除 Inbound '{inbound_id}' 吗？此操作不可撤销。"),
            actions=[
                ft.TextButton("取消", on_click=cancel_delete),
                ft.TextButton("删除", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        self.page.dialog = dialog
        dialog.open = True
        self.page.update()

    def _on_refresh(self, e):
        """刷新按钮点击事件。"""
        self._refresh_inbound_list()

    def _show_inbound_editor(
        self, inbound_id: Optional[str] = None, inbound_config: Any = None
    ):
        logger.info(f"开始显示编辑器: inbound_id={inbound_id}")
        try:
            # 创建完整编辑器对话框
            is_edit_mode = inbound_id is not None
            title_text = (
                f"编辑 Inbound: {inbound_id}" if is_edit_mode else "添加 Inbound"
            )
            logger.info(f"创建完整编辑器对话框: {title_text}")

            # 初始化数据
            initial_data = {}
            if is_edit_mode and inbound_config:
                config_dict = inbound_config.model_dump()
                initial_data = config_dict

            # 创建动态编辑器
            def on_save_callback(data: Dict[str, Any]):
                self._save_inbound(inbound_id, data, is_edit_mode)

            def on_cancel_callback():
                self._close_editor()

            self.dynamic_editor = DynamicInboundEditor(
                title=title_text,
                initial_data=initial_data,
                on_save=on_save_callback,
                on_cancel=on_cancel_callback,
            )

            # 设置页面引用以便更新
            self.dynamic_editor.page = self.page

            # 构建编辑器界面
            editor_content = self.dynamic_editor.build()

            # 创建完整对话框
            self.editor_dialog = ft.AlertDialog(
                title=ft.Text(""),  # 标题由编辑器内部处理
                content=ft.Container(
                    content=editor_content,
                    width=600,
                    height=500,
                ),
                modal=True,
            )

            # 使用 overlay 显示对话框
            logger.info("使用 overlay 显示完整编辑器")
            if self.editor_dialog not in self.page.overlay:
                self.page.overlay.append(self.editor_dialog)
            self.editor_dialog.open = True
            self.page.update()
            logger.info("完整编辑器已显示")
        except Exception as e:
            logger.exception(f"显示编辑器失败: {e}")

    def _save_inbound(
        self, inbound_id: Optional[str], data: Dict[str, Any], is_edit_mode: bool
    ):
        """保存 Inbound 配置。

        Args:
            inbound_id: Inbound ID
            data: 配置数据
            is_edit_mode: 是否为编辑模式
        """
        try:
            # 验证数据
            inbound_type = data.get("type")
            if not inbound_type:
                raise ValueError("必须选择 Inbound 类型")

            config_id = data.get("id")
            if not config_id:
                raise ValueError("必须提供 Inbound ID")

            # 根据类型创建配置对象
            inbound_config = self._create_inbound_config(inbound_type, data)

            # 添加或更新配置
            if is_edit_mode and inbound_id:
                # 删除旧配置
                self.config_manager.remove_inbound(inbound_id)

            # 添加新配置
            success = self.config_manager.add_inbound(config_id, inbound_config)
            if not success:
                raise ValueError("添加 Inbound 配置失败")

            logger.info(f"保存 Inbound 配置成功: {config_id} ({inbound_type})")

            # 关闭编辑器并刷新列表
            self._close_editor()
            self._refresh_inbound_list()

        except Exception as e:
            logger.error(f"保存 Inbound 配置失败: {e}")
            raise e  # 重新抛出异常，让编辑器显示错误信息

    def _create_inbound_config(
        self, inbound_type: str, data: Dict[str, Any]
    ) -> InboundConfig:
        """根据类型和数据创建 Inbound 配置对象。

        Args:
            inbound_type: Inbound 类型
            data: 配置数据

        Returns:
            InboundConfig: 配置对象
        """
        # 清理数据，移除空值和无关字段
        clean_data = {k: v for k, v in data.items() if v is not None and v != ""}

        # 获取配置类
        if inbound_type not in self.config_classes:
            raise ValueError(f"不支持的 Inbound 类型: {inbound_type}")

        config_class = self.config_classes[inbound_type]

        try:
            # 处理特殊字段
            processed_data = self._process_special_fields(inbound_type, clean_data)

            # 创建配置对象
            return config_class(**processed_data)

        except Exception as e:
            logger.error(f"创建 {inbound_type} 配置对象失败: {e}")
            logger.error(f"配置数据: {clean_data}")
            raise ValueError(f"配置数据验证失败: {str(e)}")

    def _process_special_fields(
        self, inbound_type: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理特殊字段。

        Args:
            inbound_type: Inbound 类型
            data: 原始数据

        Returns:
            Dict[str, Any]: 处理后的数据
        """
        processed_data = data.copy()

        if inbound_type == InboundTypes.HttpClient:
            # HTTP Client 需要 requests 字段，如果没有提供则创建空列表
            if "requests" not in processed_data:
                processed_data["requests"] = []

        elif inbound_type == InboundTypes.Focas:
            # Focas 只需要 type 字段
            processed_data = {"type": inbound_type}

        # 处理 textarea 字段（如 points, param_config）
        for field_name, value in data.items():
            if isinstance(value, str) and field_name in [
                "points",
                "param_config",
                "requests",
            ]:
                # 如果是字符串且应该是复杂类型，尝试解析或设置为默认值
                if field_name == "points":
                    processed_data[field_name] = None  # Modbus points 暂时设为 None
                elif field_name == "param_config":
                    processed_data[field_name] = {}  # 设为空字典
                elif (
                    field_name == "requests" and inbound_type == InboundTypes.HttpClient
                ):
                    processed_data[field_name] = []  # 设为空列表

        return processed_data

    def _close_editor(self, e=None):
        logger.info(f"_close_editor called. Event: {e}")
        if self.editor_dialog is not None:
            logger.info(f"Closing dialog: {self.editor_dialog}")
            self.editor_dialog.open = False
            # 从 overlay 中移除对话框
            if self.editor_dialog in self.page.overlay:
                self.page.overlay.remove(self.editor_dialog)
            self.page.update()
        self.editor_dialog = None
        logger.info("Dialog closed and page updated.")
