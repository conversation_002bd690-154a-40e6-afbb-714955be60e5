#!/usr/bin/env python3
"""测试 HTTP Client 编辑器功能。"""

from beezer.gui.views.inbound_view import DynamicInboundEditor
from beezer.type_model import InboundTypes


def test_http_client_fields():
    """测试 HTTP Client 字段生成。"""
    print("测试 HTTP Client 动态字段生成...")
    
    # 创建编辑器
    editor = DynamicInboundEditor(
        title="测试 HTTP Client 编辑器",
        initial_data={"type": InboundTypes.HttpClient},
        on_save=lambda data: print(f"保存数据: {data}"),
        on_cancel=lambda: print("取消编辑")
    )
    
    # 获取字段
    fields = editor._get_fields_for_type(InboundTypes.HttpClient)
    
    print(f"\n=== HTTP Client 字段 ===")
    print(f"生成的字段数量: {len(fields)}")
    
    for field in fields:
        print(f"  - {field.name}: {field.label}")
        print(f"    类型: {field.field_type}")
        print(f"    必填: {field.required}")
        print(f"    默认值: {field.default}")
        if field.field_type == "dropdown":
            print(f"    选项: {field.options}")
        
        # 测试默认值生成
        default_value = editor._get_default_value_for_field(field)
        print(f"    生成的默认值: {repr(default_value)}")
        print()


def test_field_value_parsing():
    """测试字段值解析。"""
    print("测试字段值解析...")
    
    editor = DynamicInboundEditor(
        title="测试解析",
        initial_data={"type": InboundTypes.HttpClient}
    )
    
    # 模拟一个 requests 字段
    from beezer.gui.components.config_editor import ConfigField
    import flet as ft
    
    field = ConfigField(
        name="requests",
        label="HTTP请求配置",
        field_type="textarea",
        required=True
    )
    
    # 创建一个模拟的文本控件
    class MockTextField:
        def __init__(self, value):
            self.value = value
    
    # 测试有效的 JSON
    editor.field_controls = {
        "requests": MockTextField('''[
  {
    "method": "GET",
    "name": "test",
    "url": "http://example.com"
  }
]''')
    }
    
    value = editor._get_field_value(field)
    print(f"解析的 JSON 值: {value}")
    print(f"类型: {type(value)}")
    
    # 测试无效的 JSON
    editor.field_controls = {
        "requests": MockTextField('invalid json')
    }
    
    value = editor._get_field_value(field)
    print(f"无效 JSON 的值: {value}")
    print(f"类型: {type(value)}")


if __name__ == "__main__":
    test_http_client_fields()
    print("\n" + "="*50 + "\n")
    test_field_value_parsing()
    print("\n测试完成!")
